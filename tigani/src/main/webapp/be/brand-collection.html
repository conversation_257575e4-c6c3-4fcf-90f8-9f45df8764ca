{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'MAINTENANCE' %}
    {% set submenu = 'BRAND_COLLECTION' %}

    <title>Manutenzione / Marchi</title>

    <!-- Page Libs -->
    {% include "be/include/snippets/plugins/datatable.html" %}
    {% include "be/include/snippets/plugins/toastify.html" %}
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/maxlength.html" %}

{% endblock %}

{% block content %}

<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Card -->
    <div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
            <!-- Title -->
            <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                    Marchi
                </h2>
            </div>
            <!-- End Title -->
            <!-- Actions -->
            <div class="flex flex-wrap items-center gap-2">
                {% if user.hasPermission('BRAND_MANAGEMENT', 'create') %}
                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-hidden focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none" onclick="createBrand()">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                    Nuovo Marchio
                </button>
                {% endif %}
            </div>
            <!-- End Actions -->
        </div>
        <!-- End Header -->
        
        <!-- DataTable Content -->        
        <div class="flex flex-col">
            <div id="brand-datatable-container">
                <!-- DataTable Header -->
                <div class="flex flex-wrap items-center gap-2 p-5">                    
                    <!-- Search -->                    
                    {% include "be/include/snippets/tables/search.html" %}                                
                    <!-- Search -->
                    <div class="flex-1 flex items-center justify-start space-x-2 xl:justify-end">                        
                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                            <!-- Filters -->
                            {% if user.hasPermission('BRAND_MANAGEMENT', 'view') %}
                                {% include "be/include/snippets/tables/filters.html" %}                                
                            {% endif %}
                            <!-- End Filters -->
                            
                            <!-- Bulk Actions -->
                            {% if user.hasPermission('BRAND_MANAGEMENT', 'edit') or user.hasPermission('BRAND_MANAGEMENT', 'delete') %}
                            <div class="flex items-center gap-2">
                                {% if user.hasPermission('BRAND_MANAGEMENT', 'edit') %}
                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" id="bulk-archive-btn" onclick="archiveSelectedBrands()" disabled>
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="21 8 12 17 3 8"/><path d="M21 8v13H3V8"/></svg>
                                    Archivia Selezionati
                                </button>
                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" id="bulk-unarchive-btn" onclick="unarchiveSelectedBrands()" disabled>
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 16v5h18v-5"/><polyline points="16 10 12 6 8 10"/><line x1="12" y1="6" x2="12" y2="16"/></svg>
                                    Ripristina Selezionati
                                </button>
                                {% endif %}
                                {% if user.hasPermission('BRAND_MANAGEMENT', 'delete') %}
                                <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-red-600 text-white hover:bg-red-700 focus:outline-hidden focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none" id="bulk-delete-btn" onclick="deleteSelectedBrands()" disabled>
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
                                    Elimina Selezionati
                                </button>
                                {% endif %}
                            </div>
                            <!-- End Bulk Actions -->
                            {% endif %}
                            
                            <!-- Date Range Picker -->
                            <div class="relative">
                                <div class="hs-datepicker-input">
                                    <input id="brand-date-range" class="py-2 px-3 pe-11 block w-full border-gray-200 shadow-sm rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Seleziona periodo" readonly>
                                    <div class="absolute inset-y-0 end-0 flex items-center pointer-events-none pe-3">
                                        <svg class="shrink-0 size-4 text-gray-400 dark:text-neutral-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/></svg>
                                    </div>
                                </div>
                            </div>
                            <!-- End Date Range Picker -->
                            
                            <!-- Export Dropdown -->
                            <div class="hs-dropdown relative inline-flex" id="hs-dropdown-datatable-with-export">
                                <button id="hs-dropdown-datatable-with-export-toggle" type="button" class="hs-dropdown-toggle py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12" y1="15" x2="12" y2="3"/></svg>
                                    Esporta
                                    <svg class="hs-dropdown-open:rotate-180 shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                                </button>

                                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg mt-2 dark:bg-neutral-800 dark:border dark:border-neutral-700 dark:divide-neutral-700 after:h-4 after:absolute after:-top-4 after:start-0 after:w-full before:h-4 before:absolute before:-bottom-4 before:start-0 before:w-full" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-datatable-with-export-toggle">
                                    <div class="p-1 space-y-0.5">
                                        <button type="button" class="w-full flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" data-hs-datatable-action-type="print">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 6 2 18 2 18 9"/><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"/><rect width="12" height="8" x="6" y="14"/></svg>
                                            Stampa
                                        </button>
                                        <button type="button" class="w-full flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" data-hs-datatable-action-type="excel">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                                            Excel
                                        </button>
                                        <button type="button" class="w-full flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" data-hs-datatable-action-type="csv">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                                            CSV
                                        </button>
                                        <button type="button" class="w-full flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" data-hs-datatable-action-type="pdf">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                                            PDF
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- End Export Dropdown -->
                        </div>
                    </div>
                </div>
                <!-- End DataTable Header -->

                <!-- DataTable -->
                <div class="overflow-hidden">
                    <table id="brand-datatable" class="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
                        <thead class="bg-gray-50 dark:bg-neutral-800">
                            <tr>
                                <th scope="col" class="ps-6 py-3 text-start">
                                    <div class="flex items-center gap-x-2">
                                        <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                            Selezione
                                        </span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-start">
                                    <div class="flex items-center gap-x-2">
                                        <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                            Codice
                                        </span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-start">
                                    <div class="flex items-center gap-x-2">
                                        <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                            Descrizione
                                        </span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-start">
                                    <div class="flex items-center gap-x-2">
                                        <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                            Data Creazione
                                        </span>
                                    </div>
                                </th>
                                <th scope="col" class="px-6 py-3 text-end">
                                    <div class="flex items-center gap-x-2">
                                        <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                            Azioni
                                        </span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                            <!-- DataTables will populate this -->
                        </tbody>
                    </table>
                </div>
                <!-- End DataTable -->
            </div>
        </div>
        <!-- End DataTable Content -->
    </div>
    <!-- End Card -->
</div>

<!-- Archived Filter Offcanvas -->
<div id="brand-archived-filter" class="hs-overlay hs-overlay-open:translate-x-0 hidden -translate-x-full fixed top-0 start-0 transition-all duration-300 transform h-full max-w-xs w-full z-[80] bg-white border-e dark:bg-neutral-800 dark:border-neutral-700" role="dialog" tabindex="-1" aria-labelledby="brand-archived-filter-label">
    <div class="flex justify-between items-center py-3 px-4 border-b dark:border-neutral-700">
        <h3 id="brand-archived-filter-label" class="font-bold text-gray-800 dark:text-white">
            Filtri
        </h3>
        <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-hidden focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-neutral-400 dark:hover:bg-neutral-600 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#brand-archived-filter">
            <span class="sr-only">Close</span>
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 6-12 12"/><path d="m6 6 12 12"/></svg>
        </button>
    </div>
    <div class="p-4">
        <div class="space-y-4">
            <!-- Archived Filter -->
            <div>
                <label class="flex items-center">
                    <input type="checkbox" id="brand_archived" class="shrink-0 mt-0.5 border-gray-200 rounded text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800">
                    <span class="text-sm text-gray-800 ms-3 dark:text-neutral-400">Mostra elementi archiviati</span>
                </label>
            </div>
        </div>
    </div>
</div>
<!-- End Archived Filter Offcanvas -->

{% endblock %}

{% block pagescript %}

    <!-- Reload -->
    <script class="reload-script-on-load">
        addRoute('BE_BRAND_DATA', '{{ routes("BE_BRAND_DATA") }}');
        addRoute('BE_BRAND_OPERATE', '{{ routes("BE_BRAND_OPERATE") }}');
        addRoute('BE_BRAND_FORM', '{{ routes("BE_BRAND_FORM") }}');
        addRoute('BE_BRAND_SAVE', '{{ routes("BE_BRAND_SAVE") }}');
    </script>

    <!-- Page Scripts -->
    <script src="{{ contextPath }}/js/pages/brand-collection.js?{{ buildNumber }}"></script>
    <script src="{{ contextPath }}/js/pages/brand-form.js?{{ buildNumber }}"></script>
{% endblock %}
