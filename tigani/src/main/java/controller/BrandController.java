package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import dao.QueryOptions;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Brand;
import pojo.RoutesPermission;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.RequestUtils;
import utils.UploadedFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class BrandController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandController.class.getName());

    public static TemplateViewRoute be_brand_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_BRAND_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_brand = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("brandId"));
        if (oid != null) {
            Brand loadedBrand = BaseDao.getDocumentById(oid, Brand.class);
            attributes.put("curBrand", loadedBrand);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Brand loadedBrand = BaseDao.getDocumentByParentId(parentId, Brand.class);
                if (loadedBrand != null) {
                    attributes.put("curBrand", loadedBrand);
                }
            }
        }

        return Core.render(Pages.BE_BRAND, attributes, request);
    };

    public static TemplateViewRoute be_brand_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("brandId"));
        if (oid != null) {
            Brand loadedBrand = BaseDao.getDocumentById(oid, Brand.class);
            attributes.put("curBrand", loadedBrand);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Brand loadedBrand = BaseDao.getDocumentByParentId(parentId, Brand.class);
                if (loadedBrand != null) {
                    attributes.put("curBrand", loadedBrand);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_BRAND_FORM, attributes, request);
    };

    public static Route be_brand_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Bson> filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("archived", DaoFiltersOperation.EQ, loadArchived));

        // search filter
        String searchValue = params.get("search[value]");
        if (StringUtils.isNotBlank(searchValue)) {
            List<Bson> searchFilters = new ArrayList<>();
            searchFilters.add(DaoFilters.getFilter("codice", DaoFiltersOperation.REGEX, searchValue));
            searchFilters.add(DaoFilters.getFilter("descrizione", DaoFiltersOperation.REGEX, searchValue));
            filters.add(DaoFilters.getOrFilter(searchFilters));
        }

        // sorting
        String sortColumn = params.get("order[0][column]");
        String sortDirection = params.get("order[0][dir]");
        String sortField = null;
        if (StringUtils.isNotBlank(sortColumn)) {
            switch (sortColumn) {
                case "0":
                    sortField = "codice";
                    break;
                case "1":
                    sortField = "descrizione";
                    break;
                case "2":
                    sortField = "creationDate";
                    break;
                default:
                    sortField = "creationDate";
                    break;
            }
        }

        // pagination
        int start = 0;
        int length = 10;
        if (StringUtils.isNotBlank(params.get("start"))) {
            start = Integer.parseInt(params.get("start"));
        }
        if (StringUtils.isNotBlank(params.get("length"))) {
            length = Integer.parseInt(params.get("length"));
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, start, length, sortField, sortDirection);
        List<Brand> brands = BaseDao.getDocumentsByFilters(Brand.class, queryOptions, null, false, false);
        long totalRecords = BaseDao.countDocumentsByFilters(Brand.class, queryOptions);

        // build JSON response for DataTables
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"draw\": ").append(params.get("draw")).append(",");
        json.append("\"recordsTotal\": ").append(totalRecords).append(",");
        json.append("\"recordsFiltered\": ").append(totalRecords).append(",");
        json.append("\"data\": [");

        for (int i = 0; i < brands.size(); i++) {
            Brand brand = brands.get(i);
            if (i > 0) json.append(",");
            json.append("[");
            json.append("\"<input type='checkbox' class='form-check-input' name='brand_checkbox' value='").append(brand.getId()).append("'>\",");
            json.append("\"<a href='#' brandId='").append(brand.getId()).append("'>").append(StringUtils.defaultString(brand.getCodice())).append("</a>\",");
            json.append("\"").append(StringUtils.defaultString(brand.getDescrizione())).append("\",");
            json.append("\"").append(brand.getCreationDate() != null ? brand.getCreationDate().toString() : "").append("\",");
            json.append("\"<div class='d-flex gap-2'>");
            
            // Edit button
            if (user.hasPermission(RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.EDIT)) {
                json.append("<button type='button' class='btn btn-sm btn-outline-primary' onclick='editBrand(\\\"").append(brand.getId()).append("\\\")' title='Modifica'>");
                json.append("<i class='ph-pencil'></i></button>");
            }
            
            // Archive/Unarchive button
            if (user.hasPermission(RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.EDIT)) {
                if (brand.getArchived()) {
                    json.append("<button type='button' class='btn btn-sm btn-outline-success' onclick='unarchiveBrand(\\\"").append(brand.getId()).append("\\\")' title='Ripristina'>");
                    json.append("<i class='ph-arrow-counter-clockwise'></i></button>");
                } else {
                    json.append("<button type='button' class='btn btn-sm btn-outline-warning' onclick='archiveBrand(\\\"").append(brand.getId()).append("\\\")' title='Archivia'>");
                    json.append("<i class='ph-archive'></i></button>");
                }
            }
            
            // Delete button
            if (user.hasPermission(RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.DELETE)) {
                json.append("<button type='button' class='btn btn-sm btn-outline-danger' onclick='deleteBrand(\\\"").append(brand.getId()).append("\\\")' title='Elimina'>");
                json.append("<i class='ph-trash'></i></button>");
            }
            
            json.append("</div>\"");
            json.append("]");
        }

        json.append("]");
        json.append("}");

        return json.toString();
    };

    public static Route be_brand_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("brandId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), requiredPermission);

        Brand newBrand;
        if (oid != null) {
            newBrand = BaseDao.getDocumentById(oid, Brand.class);
            RequestUtils.mergeFromParams(params, newBrand);
        } else {
            newBrand = RequestUtils.createFromParams(params, Brand.class);
        }

        if (newBrand != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newBrand);
                newBrand.setId(oid);

                BaseDao.insertLog(user, newBrand, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newBrand);
                BaseDao.insertLog(user, newBrand, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_brand_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), requiredPermission);

        String brandIds = params.get("brandIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(brandIds)) {
            String[] ids = brandIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Brand tmpBrand = BaseDao.getDocumentById(oid, Brand.class);
                    if (tmpBrand != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpBrand);
                                BaseDao.insertLog(user, tmpBrand, LogType.DELETE);
                                break;
                            case "archive":
                                tmpBrand.setArchived(true);
                                BaseDao.updateDocument(tmpBrand);
                                BaseDao.insertLog(user, tmpBrand, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpBrand.setArchived(false);
                                BaseDao.updateDocument(tmpBrand);
                                BaseDao.insertLog(user, tmpBrand, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
