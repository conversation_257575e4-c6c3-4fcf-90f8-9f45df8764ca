const BrandForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitBrand();
        _componentInputFormatting();
    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            errorPlacement: function (error, element) {
                // Custom error placement
                if (element.parents('.input-group').length) {
                    error.appendTo(element.parents('.input-group').parent());
                } else if (element.parents('.form-check').length) {
                    error.appendTo(element.parents('.form-check').parent());
                } else {
                    error.insertAfter(element);
                }
            },
            rules: {
                codice: {
                    required: true,
                    minlength: 1,
                    maxlength: 50,
                    brandCode: true
                },
                descrizione: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                }
            },
            messages: {
                codice: {
                    required: "Il codice è obbligatorio",
                    minlength: "Il codice deve essere di almeno 1 carattere",
                    maxlength: "Il codice non può superare i 50 caratteri"
                },
                descrizione: {
                    required: "La descrizione è obbligatoria",
                    minlength: "La descrizione deve essere di almeno 2 caratteri",
                    maxlength: "La descrizione non può superare i 100 caratteri"
                }
            }
        });

        // custom validation rules for brand
        $.validator.addMethod('brandCode', function (value) {
            return /^[A-Z0-9\-_]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole, numeri, trattini e underscore.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    // Delete button functionality
    const _componentDeleteButton = function () {
        $(document).on('click', '#delete-brand-btn', function (e) {
            e.preventDefault();

            // Check permissions
            if (!hasPermission('BRAND_MANAGEMENT', 'delete')) {
                showToast('Non hai i permessi per eliminare marchi.', 'error');
                return;
            }

            const brandId = $('input[name="id"]').val();
            if (!brandId) {
                showToast('Errore: ID marchio non trovato', 'error');
                return;
            }

            if (confirm('Sei sicuro di voler eliminare definitivamente questo marchio? Questa operazione non può essere annullata.')) {
                const formData = new FormData();
                formData.append('brandIds', brandId);
                formData.append('operation', 'delete');

                $.ajax({
                    url: appRoutes.get("BE_BRAND_OPERATE"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        showToast('Marchio eliminato correttamente.', 'success');
                        
                        // Close offcanvas and reload table
                        const offcanvasElement = document.querySelector('[data-hs-overlay-close]');
                        if (offcanvasElement) {
                            offcanvasElement.click();
                        }
                        
                        // Reload table if available
                        if (typeof BrandCollection !== 'undefined' && BrandCollection.reloadTable) {
                            BrandCollection.reloadTable();
                        }
                    },
                    error: function (error) {
                        showToast(error.responseText || 'Errore durante l\'eliminazione', 'error');
                        console.error('Error during brand delete:', error);
                    }
                });
            }
        });
    };

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions
        const hasEditPermission = hasPermission('BRAND_MANAGEMENT', 'edit');
        const hasCreatePermission = hasPermission('BRAND_MANAGEMENT', 'create');
        const hasDeletePermission = hasPermission('BRAND_MANAGEMENT', 'delete');

        // Disable form fields if no edit permission
        if (!hasEditPermission && !hasCreatePermission) {
            $('#brand-edit-offcanvas input, #brand-edit-offcanvas textarea, #brand-edit-offcanvas select').prop('readonly', true);
            $('#brand-edit-offcanvas button[type="submit"]').hide();
        }

        // Hide delete button if no delete permission
        if (!hasDeletePermission) {
            $('#delete-brand-btn').hide();
        }

        // Show appropriate buttons based on context
        const brandId = $('input[name="id"]').val();
        if (!brandId) {
            // New brand - hide delete button
            $('#delete-brand-btn').hide();
        }
    };

    // Form submission handling
    const _componentSubmitBrand = function () {
        var idForm = "brand-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewBrand = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewBrand) {
                    if (!hasPermission('BRAND_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare marchi.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('BRAND_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare marchi.', 'error');
                        return;
                    }
                }

                // Show loading state
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Salvando...');

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        // Reset button state
                        submitBtn.prop('disabled', false).html(originalText);

                        showToast(isNewBrand ? 'Marchio creato correttamente.' : 'Marchio aggiornato correttamente.', 'success');
                        
                        // Close offcanvas
                        const offcanvasElement = document.querySelector('[data-hs-overlay-close]');
                        if (offcanvasElement) {
                            offcanvasElement.click();
                        }
                        
                        // Reload table if available
                        if (typeof BrandCollection !== 'undefined' && BrandCollection.reloadTable) {
                            BrandCollection.reloadTable();
                        }
                    },
                    error: function (xhr, status, error) {
                        // Reset button state
                        submitBtn.prop('disabled', false).html(originalText);

                        let errorMessage = 'Errore durante il salvataggio del marchio.';
                        
                        if (xhr.responseText) {
                            try {
                                const errorResponse = JSON.parse(xhr.responseText);
                                if (errorResponse.message) {
                                    errorMessage = errorResponse.message;
                                } else {
                                    errorMessage = xhr.responseText;
                                }
                            } catch (e) {
                                errorMessage = xhr.responseText;
                            }
                        } else if (xhr.status === 403) {
                            errorMessage = 'Non hai i permessi necessari per questa operazione.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Risorsa non trovata.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Errore interno del server.';
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during brand save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Codice field - uppercase transformation and format validation
        $('input[name="codice"]').on('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z0-9\-_]/g, '');
        });

        // Descrizione field - trim whitespace
        $('input[name="descrizione"]').on('blur', function() {
            this.value = this.value.trim();
        });

        // Prevent form submission on Enter key in input fields (except textarea)
        $('input').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                $(this).blur(); // Trigger blur event for formatting
            }
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Auto-initialize when included
$(document).ready(function() {
    // Don't auto-initialize here as it will be called by the offcanvas loader
});
