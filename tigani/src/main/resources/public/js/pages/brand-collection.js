// Global variables for table management
const BrandCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        // Initialize Preline UI DataTable with JavaScript configuration
        const datatableConfig = {
            paging: true,
            pageLength: 10,
            searching: true,
            ordering: true,
            info: true,
            lengthChange: true,
            scrollCollapse: true,
            ajax: {
                url: appRoutes.get("BE_BRAND_DATA"),
                type: 'GET'
            },
            select: {
                style: 'multi',
                selector: 'td:select-checkbox'
            },
            responsive: {
                details: {
                    type: 'column',
                    target: -1,
                }
            },
            columnDefs: [
                {
                    targets: 0,
                    orderable: false,
                    className: 'select-checkbox',
                    checkboxes: {
                        selectRow: true
                    }
                },
                {
                    targets: -1,
                    orderable: false,
                    className: 'text-center'
                }
            ],
            order: [[2, 'desc']], // Sort by creation date descending
            dom: '<"top"<"left-col"<"length"l>><"center-col"<"search"f>><"right-col"<"buttons"B>>>rt<"bottom"<"left-col"<"info"i>><"right-col"<"pagination"p>>>',
            buttons: [
                {
                    extend: 'print',
                    text: 'Stampa',
                    className: 'btn btn-outline-secondary'
                },
                {
                    extend: 'excel',
                    text: 'Excel',
                    className: 'btn btn-outline-secondary'
                },
                {
                    extend: 'csv',
                    text: 'CSV',
                    className: 'btn btn-outline-secondary'
                },
                {
                    extend: 'pdf',
                    text: 'PDF',
                    className: 'btn btn-outline-secondary'
                }
            ],
            language: {
                "sEmptyTable": "Nessun dato presente nella tabella",
                "sInfo": "Vista da _START_ a _END_ di _TOTAL_ elementi",
                "sInfoEmpty": "Vista da 0 a 0 di 0 elementi",
                "sInfoFiltered": "(filtrati da _MAX_ elementi totali)",
                "sInfoPostFix": "",
                "sInfoThousands": ".",
                "sLengthMenu": "Visualizza _MENU_ elementi",
                "sLoadingRecords": "Caricamento...",
                "sProcessing": "Elaborazione...",
                "sSearch": "Cerca:",
                "sZeroRecords": "La ricerca non ha portato alcun risultato.",
                "oPaginate": {
                    "sFirst": "Inizio",
                    "sPrevious": "Precedente",
                    "sNext": "Successivo",
                    "sLast": "Fine"
                },
                "oAria": {
                    "sSortAscending": ": attiva per ordinare la colonna in ordine crescente",
                    "sSortDescending": ": attiva per ordinare la colonna in ordine decrescente"
                },
                "select": {
                    "rows": {
                        "_": "Hai selezionato %d righe",
                        "0": "Clicca su una riga per selezionarla",
                        "1": "Hai selezionato 1 riga"
                    }
                }
            }
        };

        const tableEl = document.getElementById('brand-datatable-container');
        const hsDataTable = new HSDataTable(tableEl, datatableConfig);

        // On draw initialize HS components and setup checkbox handlers
        hsDataTable.dataTable.on('draw', function() {
            HSStaticMethods.autoInit();
            _setupCheckboxHandlers();
        });

        window.brandsDataTable = hsDataTable;

        const buttons = document.querySelectorAll('#hs-dropdown-datatable-with-export .hs-dropdown-menu button');
        buttons.forEach((btn) => {
            const type = btn.getAttribute('data-hs-datatable-action-type');

            btn.addEventListener('click', () => hsDataTable.dataTable.button(`.buttons-${type}`).trigger());
        });

        // Initial setup of checkbox handlers
        _setupCheckboxHandlers();
    };

    // Setup checkbox handlers for bulk operations
    const _setupCheckboxHandlers = function() {
        // Update bulk action buttons based on selection
        const updateBulkButtons = function() {
            const selectedRows = _getSelectedRows();
            const hasSelection = selectedRows.length > 0;
            
            // Enable/disable bulk action buttons
            const bulkArchiveBtn = document.getElementById('bulk-archive-btn');
            const bulkUnarchiveBtn = document.getElementById('bulk-unarchive-btn');
            const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
            
            if (bulkArchiveBtn) bulkArchiveBtn.disabled = !hasSelection;
            if (bulkUnarchiveBtn) bulkUnarchiveBtn.disabled = !hasSelection;
            if (bulkDeleteBtn) bulkDeleteBtn.disabled = !hasSelection;
        };

        // Listen for checkbox changes
        $(document).off('change', 'input[name="brand_checkbox"]').on('change', 'input[name="brand_checkbox"]', function() {
            updateBulkButtons();
        });

        // Listen for select all checkbox
        $(document).off('change', '#select-all-brands').on('change', '#select-all-brands', function() {
            const isChecked = $(this).is(':checked');
            $('input[name="brand_checkbox"]').prop('checked', isChecked);
            updateBulkButtons();
        });

        // Initial state
        updateBulkButtons();
    };

    // Get selected rows
    const _getSelectedRows = function() {
        const selectedRows = [];
        $('input[name="brand_checkbox"]:checked').each(function() {
            selectedRows.push({
                id: $(this).val(),
                element: $(this).closest('tr')
            });
        });
        return selectedRows;
    };

    // Clear selection
    const _clearSelection = function() {
        $('input[name="brand_checkbox"]').prop('checked', false);
        $('#select-all-brands').prop('checked', false);
        _setupCheckboxHandlers(); // Refresh button states
    };

    // Reload table
    const _reloadTable = function() {
        if (window.brandsDataTable && window.brandsDataTable.dataTable) {
            window.brandsDataTable.dataTable.ajax.reload(null, false);
        }
    };

    // Edit Brand functionality
    function _componentEditBrand() {
        // Edit Brand Click Handler for table rows
        $(document).on('click', 'a[brandId]', function(e) {
            e.preventDefault();

            try {
                const brandId = $(this).attr('brandId');
                const brandName = $(this).text().trim();

                if (!brandId) {
                    showToast('Errore: ID marchio non trovato', 'error');
                    return;
                }

                // Check if required functions are available
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_BRAND_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Marchio: ' + (brandName || 'Sconosciuto'),
                    url: appRoutes.get('BE_BRAND_FORM') + '?brandId=' + encodeURIComponent(brandId),
                    entity: 'brand',
                    entityId: brandId,
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            // Initialize brand form components after content is loaded
                            if (typeof BrandForm !== 'undefined' && BrandForm.init) {
                                BrandForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        });
    }

    // Create Brand functionality
    function _componentCreateBrand() {
        window.createBrand = function() {
            try {
                // Check if required functions are available
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_BRAND_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Nuovo Marchio',
                    url: appRoutes.get('BE_BRAND_FORM'),
                    entity: 'brand',
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            // Initialize brand form components after content is loaded
                            if (typeof BrandForm !== 'undefined' && BrandForm.init) {
                                BrandForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening create form:', error);
                showToast('Errore nell\'apertura del modulo di creazione', 'error');
            }
        };
    }

    // Archive selected brands
    function _componentArchiveSelectedBrands() {
        window.archiveSelectedBrands = function() {
            const selectedRows = _getSelectedRows();
            if (selectedRows.length === 0) {
                showToast('Seleziona almeno un marchio da archiviare', 'warning');
                return;
            }

            if (!confirm('Sei sicuro di voler archiviare i marchi selezionati?')) {
                return;
            }

            const brandIds = selectedRows.map(row => row.id).join(',');
            const formData = new FormData();
            formData.append('brandIds', brandIds);
            formData.append('operation', "archive");
            formData.append('fromArchived', $("#brand_archived:checked").length > 0);

            $.ajax({
                url: appRoutes.get("BE_BRAND_OPERATE"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    _reloadTable();
                    showToast('Dati salvati correttamente.', 'success');
                    _clearSelection();
                },
                error: function (error) {
                    showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                    console.error('Error during brand archive', error);
                }
            });
        }
    }

    // Unarchive selected brands
    function _componentUnarchiveSelectedBrands() {
        window.unarchiveSelectedBrands = function() {
            const selectedRows = _getSelectedRows();
            if (selectedRows.length === 0) {
                showToast('Seleziona almeno un marchio da ripristinare', 'warning');
                return;
            }

            if (!confirm('Sei sicuro di voler ripristinare i marchi selezionati?')) {
                return;
            }

            const brandIds = selectedRows.map(row => row.id).join(',');
            const formData = new FormData();
            formData.append('brandIds', brandIds);
            formData.append('operation', "unarchive");
            formData.append('fromArchived', $("#brand_archived:checked").length > 0);

            $.ajax({
                url: appRoutes.get("BE_BRAND_OPERATE"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    _reloadTable();
                    showToast('Dati salvati correttamente.', 'success');
                    _clearSelection();
                },
                error: function (error) {
                    showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                    console.error('Error during brand unarchive', error);
                }
            });
        }
    }

    // Delete selected brands
    function _componentDeleteSelectedBrands() {
        window.deleteSelectedBrands = function() {
            const selectedRows = _getSelectedRows();
            if (selectedRows.length === 0) {
                showToast('Seleziona almeno un marchio da eliminare', 'warning');
                return;
            }

            if (!confirm('Sei sicuro di voler eliminare definitivamente i marchi selezionati? Questa operazione non può essere annullata.')) {
                return;
            }

            const brandIds = selectedRows.map(row => row.id).join(',');
            const formData = new FormData();
            formData.append('brandIds', brandIds);
            formData.append('operation', "delete");
            formData.append('fromArchived', $("#brand_archived:checked").length > 0);

            $.ajax({
                url: appRoutes.get("BE_BRAND_OPERATE"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    _reloadTable();
                    showToast('Marchi eliminati correttamente.', 'success');
                    _clearSelection();
                },
                error: function (error) {
                    showToast(error.responseText || 'Errore durante l\'eliminazione', 'error');
                    console.error('Error during brand delete', error);
                }
            });
        }
    }

    // Single row actions
    function _componentSingleRowActions() {
        // Edit brand
        window.editBrand = function(brandId) {
            if (!brandId) {
                showToast('Errore: ID marchio non trovato', 'error');
                return;
            }

            try {
                if (typeof createDynamicOffcanvas !== 'function') {
                    showToast('Errore: funzione offcanvas non disponibile', 'error');
                    return;
                }

                if (!appRoutes.has('BE_BRAND_FORM')) {
                    showToast('Errore: route non configurata', 'error');
                    return;
                }

                const offcanvas = createDynamicOffcanvas({
                    title: 'Modifica Marchio',
                    url: appRoutes.get('BE_BRAND_FORM') + '?brandId=' + encodeURIComponent(brandId),
                    entity: 'brand',
                    entityId: brandId,
                    onContentLoaded: function(offcanvasElement, contentContainer) {
                        try {
                            if (typeof BrandForm !== 'undefined' && BrandForm.init) {
                                BrandForm.init();
                            }
                        } catch (initError) {
                            console.error('Error initializing form:', initError);
                            showToast('Errore nell\'inizializzazione del modulo', 'error');
                        }
                    }
                });
            } catch (error) {
                console.error('Error opening edit form:', error);
                showToast('Errore nell\'apertura del modulo di modifica', 'error');
            }
        };

        // Archive brand
        window.archiveBrand = function(brandId) {
            if (!confirm('Sei sicuro di voler archiviare questo marchio?')) {
                return;
            }
            _performSingleRowAction(brandId, 'archive');
        };

        // Unarchive brand
        window.unarchiveBrand = function(brandId) {
            if (!confirm('Sei sicuro di voler ripristinare questo marchio?')) {
                return;
            }
            _performSingleRowAction(brandId, 'unarchive');
        };

        // Delete brand
        window.deleteBrand = function(brandId) {
            if (!confirm('Sei sicuro di voler eliminare definitivamente questo marchio? Questa operazione non può essere annullata.')) {
                return;
            }
            _performSingleRowAction(brandId, 'delete');
        };
    }

    function _performSingleRowAction(brandId, operation) {
        const formData = new FormData();
        formData.append('brandIds', brandId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#brand_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_BRAND_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during brand operation', error);
            }
        });
    }

    // Archived filter functionality
    function _componentArchivedFilter() {
        // Handle archived filter change
        $(document).on('change', '#brand_archived', function() {
            const isArchived = $(this).is(':checked');

            // Add archived parameter to DataTable AJAX request
            if (window.brandsDataTable && window.brandsDataTable.dataTable) {
                const table = window.brandsDataTable.dataTable;
                const settings = table.settings()[0];

                // Update ajax data function
                settings.ajax.data = function(d) {
                    d.archived = isArchived;
                    return d;
                };

                // Reload table
                table.ajax.reload();
            }
        });
    }

    // Date range picker functionality
    function _componentDateRangePicker() {
        // Initialize date range picker if element exists
        const dateRangeInput = document.getElementById('brand-date-range');
        if (dateRangeInput) {
            // Initialize Preline datepicker with range mode
            HSDatepicker.init(dateRangeInput, {
                mode: 'range',
                dateFormat: 'DD/MM/YYYY',
                onSelect: function(selectedDates) {
                    // Handle date range selection
                    if (selectedDates.length === 2) {
                        // Add date range filtering to DataTable
                        if (window.brandsDataTable && window.brandsDataTable.dataTable) {
                            const table = window.brandsDataTable.dataTable;
                            const settings = table.settings()[0];

                            settings.ajax.data = function(d) {
                                d.dateFrom = selectedDates[0];
                                d.dateTo = selectedDates[1];
                                if ($("#brand_archived:checked").length > 0) {
                                    d.archived = true;
                                }
                                return d;
                            };

                            table.ajax.reload();
                        }
                    }
                }
            });
        }
    }

    // Initialize all components
    const init = function () {
        _componentDatatable();
        _componentEditBrand();
        _componentCreateBrand();
        _componentArchiveSelectedBrands();
        _componentUnarchiveSelectedBrands();
        _componentDeleteSelectedBrands();
        _componentSingleRowActions();
        _componentArchivedFilter();
        _componentDateRangePicker();
    };

    // Return objects assigned to module
    return {
        init: init,
        reloadTable: _reloadTable
    };
}();

// Initialize when DOM is ready
$(document).ready(function() {
    BrandCollection.init();
});
